# Overview

This document defines the structural hierarchy of the SIT Platform, covering modules, assignments, assignment groups, chat architecture, and threaded discussions.
The purpose is to establish a standardized model to implement and integrate related features

2.  Entity Hierarchy and Relationships

a. Module Level

A Module represents a course or subject.

Each module contains one or more Assignments.

A student can be enrolled in multiple modules simultaneously.

An instructor can be assigned to manage multiple modules.

b. Assignment Level

Each Assignment belongs to exactly one Module.

A module can contain multiple assignments at the same time.

An assignment is created for all students enrolled in the module.

Assignments have separate deadlines, file repositories, and group allocations.

c. Assignment Group Level

Within each assignment, students are divided into Assignment Groups.

Each student can belong to only one group per assignment.

A group is unique to a specific assignment and does not persist across assignments.

Each assignment can contain multiple groups.

Instructors can be assigned to one or more groups within an assignment.

d. Chat Level

Each Assignment Group has one dedicated Group Chat.

The Group Chat is scoped to the assignment group only and is not shared across groups or assignments.

Both students and instructors (assigned to the group) can send messages and upload files in this chat.

Instructors are tagged when they post in group chats.

e. Thread Level

Within each Group Chat, users can create multiple threads.

Threads are used to organize discussions by topic.

Each thread belongs to a single Group Chat.

Files uploaded in threads are associated with the thread and also surfaced in the "Temporary" file repository of the assignment group.
