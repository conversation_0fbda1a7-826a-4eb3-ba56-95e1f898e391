Description: As an Instructor, I want to control the visibility, categorization, and metadata of my files, so that I can manage my assignment materials efficiently and release them to students at the appropriate time.

FR1: File Visibility Button

The system must provide the Instructor with a mechanism (button) to change a file's visibility status between Hidden and Visible.

FR2: Assignment-Level Organization

The Instructor's file repository interface must be organized at the Assignment level.

FR3: File Repository

Within the Instructor's file repository, the file list must be divided into two default repositories: Temporary files and Permanent files.

The Temporary files: must aggregate all files sent via chat from all Assignment Groups belonging to that assignment.
The Permanent files: the instructor can upload files here
FR4: File Tagging

The system must allow an Instructor to add a Tag (optional) for each file.
The list of tags will be automatically generated based on the names of the existing threads or conversations - each thread name will appear as a tag option in the list
FR5: File Description

The system must allow an Instructor to add or edit a Description for each file, with a maximum length of 120 characters.

FR6: The file list displayed to Instru<PERSON> must include the following columns:

Name, Uploaded by, Last modified, File size, Hidden/Visible, Tag, Description, Download

FR7: Download Functionality

When clicked, the file shall be downloaded directly to the user's device using the browser's default download behavior.

No file preview, thumbnail, or modal view is required for this MVP phase
