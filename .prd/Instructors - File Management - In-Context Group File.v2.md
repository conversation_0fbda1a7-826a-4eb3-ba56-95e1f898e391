Description: As an Instructor, I want to use the "File" tab within a specific group's workspace, also organized into "Main", "Draft", and "Deleted" tabs, to view all relevant files in one place, giving me the complete context of official materials, student work, chat attachments, and deleted items pertinent to that single group.

Access Flow:
Login → Choose Module → Choose Assignment → Choose Assignment Group → Choose File tab

Functional Requirements:
FR1: In-Group Tab Access and Structure

When an Instructor is viewing a specific group chat, the system must provide a "File" tab

This "File" tab must include three sub-tabs: "Main", "Draft", and "Deleted"

FR2: In-Group "Main" Tab Content

The "Main" tab must display a combined list including:

All official files were uploaded by the Instructor for the entire assignment

All permanent files uploaded by students are only within that specific group

The instructor can:
Download any files
Cannot upload file via this tab
Cannot delete files in this tab
FR3: In-Group "Draft" Tab Content

The "Draft" tab must display a combined list including:

All files attached by the Instructor in that specific group chat in the assignment

All files attached by students in the chat are only for that specific group

The instructor can:
Download any files
FR4: In-Group "Deleted" Tab Content & Functionality

The "Deleted" tab must display all soft-deleted files from students within that specific group

The system must allow the instructor to download any file listed in this tab

The system must not allow the instructor to restore files that were deleted by students
