Description: As an Instructor, I want to upload files in different ways (formally for an assignment or informally via chat), so that I can provide both official, long-term materials and quick, temporary references

Functional Requirement

FR1: Assignment File Upload

The system must allow an Instructor to upload files at the Assignment level.

FR2: File Upload Methods and Classification

The instructors can upload files in 2 ways:

Via File Management: the system must automatically classify that file as Permanent
Uploaded as an attachment in Chat: the system must automatically classify that file as Temporary
FR3: Temporary File Deletion Policy

Files classified as Temporary must be automatically deleted by the system after 180 days from the upload date.

FR4: Default File Visibility

All uploaded files, regardless of the method, must have a default visibility status of Hidden.

Then the instructor can change to visible.
