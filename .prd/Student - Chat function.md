Description: As a Student, I want to collaborate with my group members and the AI teammate in a dedicated chat space for our assignment, so that we can work through problems together, get guided help from the AI, and have a single, persistent place for all our team communication and shared resources.

Function Requirements

FR1: Access Point

→ There are two possible entry points to the Group Chat (Assignment Group):

Via LMS Deep Link
If the student clicks a deep link from the LMS, they will be directly navigated to the Threads of the corresponding Group Chat (Assignment Group).
Via Platform Login
The system will check whether the user is already logged in:
If already logged in:
The platform will redirect the user to the last visited Threads, based on session history.
If not logged in:
The user will go through the standard navigation flow:
Login → Dashboard → Select Module → Select Assignment → Enter Group Chat.
FR2: Student Access

A Student must only be able to access the single group chat for the specific assignment group they are a member of.

Other FRs: Refer to FR2 to FR14 in Instructor - Chat function file
