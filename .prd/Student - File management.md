Description: As a Student, I want to easily find all relevant, visible assignment files for my assignment group, so that I have the necessary resources to complete my work

FR1: File Visibility by Status

The system must only display files to Students that have a status of Visible.

FR2: File Visibility by Assignment Group

Students can only view files that belong to the Assignment Group they are part of.
In other words, they cannot access files uploaded in other assignment groups.
FR3: File Repositories

The system must provide Students with a file management screen featuring three default repositories: Assignment, Temporary, and Permanent.

The Assignment repository: Must display all files uploaded by the Instructor as Permanent that are marked as Visible.
The Temporary repository: Must aggregate all files sent via chat within the Student's specific Assignment Group
The Permanent repository: Must display all files uploaded by the Student themself
FR4: Student File Upload

The system must provide a file upload function for Students. These files will be saved to their Permanent repository

FR5: The file list displayed to Students (in all three repositories) must include the following columns:

Name, Uploaded by, Last modified, File size, Tag, Description.
