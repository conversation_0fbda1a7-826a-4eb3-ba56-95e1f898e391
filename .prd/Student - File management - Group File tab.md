Description: As a Student, I want to access a single, well-organized "File" tab within my assignment group, which is separated into "Learning Material", "Main", "Draft/Temporary", and "Deleted" tabs, so that I can easily find official course materials, manage my group's work files, review chat attachments, and handle my own deleted items.

Access Flow:
Login → Choose Module → Choose Assignment → Be navigated to the specific Assignment Group → Choose File tab

Functional Requirements
FR1: Tab Access and Structure

When a student is within their assignment group, the system must provide a "File" tab.

Inside this "File" tab, the system must display four sub-tabs: "Learning Material", "Main", "Draft/Temporary", and "Deleted".

FR2: "Learning Material" Tab Functionality & Content

The "Learning Material" tab must contain all official files uploaded by the instructor for the entire assignment.

The student can download these files

FR3: "Main" Tab Functionality & Content

The "Main" tab must contain all permanent files uploaded by any member of the student's group, including the student themselves.

Students can upload new files to this tab.

Students can only soft-delete files that they have personally uploaded, and other members have uploaded

Students can move any file to the "Draft/Temporary" Tab"
Students can download any files.
FR4: "Draft/Temporary" Tab Functionality & Content

The "Draft/Temporary" tab must display a combined list of all files sent via the group chat by:

Any student within that specific group.

The instructor within that specific group chat.

FR5: "Deleted" Tab Functionality & Content

The "Deleted" tab must display a combined list of all soft-deleted files from:
Groupmates within that specific group.
The students themselves.
The system must automatically and permanently delete any file in this tab after 30 days.

Students can restore any files
