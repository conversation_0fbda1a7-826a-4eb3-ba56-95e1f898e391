# Instructor - File management - Delete

Description: As an Instructor, I want to permanently delete a specific file that I have uploaded to the "Permanent files" repository, so that I can remove outdated, incorrect, or irrelevant materials and keep my course content clean and up-to-date.

Functional Requirements

FR1: Hard Delete Mechanic

When a file deletion is executed, the system must perform a hard delete, permanently removing the file and its associated metadata from the system storage entirely.

FR2: Deletion Confirmation

Before executing a delete action, the system must display a confirmation dialog ("Are you sure you want to permanently delete this file? This action cannot be undone.") to prevent accidental data loss.

FR3: Permission to Delete - Ownership Rule

A user (Instructor or Student) must only be able to delete files that they have uploaded by themselves (i.e., they are the file owner).

The system must prevent users from deleting files they do not own. Specifically, a ztudent cannot delete files uploaded by an Instructor or another student, and vice versa.

FR4: Instructor Deletion Access

The system must provide a "Delete" option for Instructors on all files within their Permanent files repository.

FR5: Student Deletion Access

The system must provide a "Delete" option for Students only on files within their Permanent repository.

FR6: Prohibition on Deleting Temporary Files

The system must not provide a manual "Delete" option for any file that is currently classified as Temporary. These files are only removed via the automatic 180-day lifecycle rule.

FR9: UI State After Deletion

Upon successful deletion, the file must be immediately removed from the file list interface for all users who previously had access to it.
