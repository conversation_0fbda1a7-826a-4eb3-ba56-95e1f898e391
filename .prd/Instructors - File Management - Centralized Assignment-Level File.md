Description: As an Instructor, I want to access a dedicated "File Management" section at the assignment level, which is separated into "Main", "Draft", and "Deleted" tabs, so that I can efficiently manage official documents, have a consolidated overview of informal files, and manage the lifecycle of deleted materials.

Access Flow:
Login → Choose Module → Choose Assignment → Choose File tab

Functional Requirement
FR1: Tab Access and Structure

When a user is within an assignment, the system must provide a "File Management" section.

Inside this section, the system must display three tabs: "Main", "Draft", and "Deleted".

FR2: "Main" Tab

The "Main" tab must only display files uploaded by the Instructor as official materials for the entire assignment.

The system must allow the Instructor to perform the following actions on files in this tab:

Upload a new file

Change the visibility status (Hidden/Visible)

Soft-delete

Write a description
Move to the "Draft" (temporary) file
Tag
Download
No Preview
FR3: "Draft" Tab

The "Draft" tab must aggregate and display all files that the Instructor has attached in messages across all group chats belonging to that assignment

The system must allow the Instructor to perform the following actions on files in this tab:

Download

FR4: "Deleted" Tab Functionality

The "Deleted" tab must contain all files that have been soft-deleted by the <PERSON><PERSON>ructor from the "Main" tab.

The system must allow the In<PERSON>ructor to perform the following actions on files in this tab:

Restore the file back to the "Main" tab

Permanently delete
