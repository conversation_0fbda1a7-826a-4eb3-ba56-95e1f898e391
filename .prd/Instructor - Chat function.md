Description: As an Instructor, I want to access and participate in the chat for each assignment group within an assignment, so that I can effectively guide student discussions, provide timely resources, and maintain a complete, unalterable record of each team's progress and collaboration.

Functional Requirements

FR1: Access Point

Log in → land on Dashboard → view list of Modules/ Assignments → click on specific Assignment Group/ Group chat → Threads/ Conversation → start chatting

FR2: Send Text Messages

The system must allow users to compose and send plain text messages within the group chat (assignment group).

FR3: User Identification

Every message sent must display the sender's full name and avatar, as pulled from the LMS.

FR4: Message Timestamps

The system must display a timestamp for each message. The format will be time-only (e.g., "10:43 AM") for recent messages, with sticky date headers (e.g., "Today", "Yesterday", "July 28, 2025") separating conversations by day.

FR5: Message Formatting

The system must provide a formatting toolbar in the message input field that supports

Bold, Italic, Strikethrough, Ordered list, Bulleted list, Links, blockquote, code, code block, attach, emoji, hide formatting (Refer to Slack)

FR6: Message Reactions

Users must be able to react to any message using a standard set of emojis (from a library like emoji-mart).

FR7: Message Replies

The system must allow users to reply to a specific message

FR8: File Attachments

Users must be able to attach and send files within the chat.

These files will be classified as Temporary by default.

Limit size: 10mb

File type: PDF, DOCX, PPTX, XLSX, CSV, PNG, JPG, TXT

Users cannot queue or upload multiple files simultaneously within a single message input.

The system shall not provide a file preview feature within the message input area. Only the file name, size, and a remove (X) option shall be displayed prior to sending.

FR9: Message Search

The system must provide a keyword search function, accessible via a UI element and the Ctrl+F / Command+F shortcut, that allows users to search the history of the current chat.

FR10: Message Immutability

The system must prohibit all users from deleting their sent messages to ensure the integrity of the conversation history.

FR11: Message Editing

After a message has been successfully edited, the system must display a persistent, inline text indicator (e.g., "(edited)") next to the message timestamp.

The system must not store or provide any means for any user to view the previous versions or edit history of a message. The most recent edit permanently replaces the prior content.

User can edit message anytime

FR712: Chat Instance Management

Group chats (Assignment Group) cannot be manually created, edited, or deleted by Instructors or Students. They must be automatically generated by the system based on group data synced from the LMS.

FR13: Instructor Access

An Instructor must be able to access and participate in the group chats of all student groups within any assignment they manage.

FR14: AI Teammate Participation

The AI companion must be included as a participant in every student group chat, capable of sending and receiving messages like a human user.
