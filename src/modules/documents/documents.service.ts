import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { CustomLogger } from '@common/logging';
import { HTTP, CustomResponse } from '@common/http';
import {
  Document,
  DocumentStatus,
  DocumentRepositoryType,
} from './entities/document.entity';
import {
  CreateDocumentDto,
  UpdateDocumentDto,
  SearchDocumentsDto,
  DocumentResponseDto,
  DeleteDocumentsResponseDto,
  DeleteDocumentResult,
} from './dto/';

import { User } from '@modules/users/entities/user.entity';
import { AssignmentGroup } from '@modules/assignment-groups/entities/assignment-group.entity';

import { isValidFileUrl } from '@common/utils';
import { applyQueryOptions } from '@common/utils/query-utils';

@Injectable()
export class DocumentsService extends HTTP {
  private readonly loggerMeta: any;

  constructor(
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AssignmentGroup)
    private readonly assignmentGroupRepository: Repository<AssignmentGroup>,
    private readonly logger: CustomLogger,
  ) {
    super();
    this.loggerMeta = { context: DocumentsService.name };
  }

  async searchDocuments(
    searchParams: SearchDocumentsDto,
    userId: string,
  ): Promise<CustomResponse> {
    try {
      this.logger.log(
        `Searching documents with params: ${JSON.stringify(searchParams)} by user: ${userId}`,
        this.loggerMeta,
      );

      // Validate userId is provided
      if (!userId || userId.trim() === '') {
        this.logger.warn('Invalid userId provided', this.loggerMeta);
        throw new BadRequestException('Valid user ID is required');
      }

      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        this.logger.warn(`User not found: ${userId}`, this.loggerMeta);
        throw new NotFoundException('User not found');
      }

      // Build query with authorization and filtering
      const queryBuilder = this.documentRepository
        .createQueryBuilder('document')
        .leftJoinAndSelect('document.uploader', 'uploader')
        .leftJoinAndSelect('document.assignmentGroup', 'assignmentGroup')
        .leftJoinAndSelect('assignmentGroup.assignment', 'assignment')
        .where('document.deletedAt IS NULL'); // Exclude soft-deleted documents

      // Authorization: Users can only see documents they have access to
      if (!user.isAdmin && !user.isInstructor) {
        // Students can only see:
        // 1. Their own documents
        // 2. VISIBLE documents in their assignment groups
        queryBuilder.andWhere(
          '(document.uploaderId = :userId OR (document.status = :visibleStatus AND assignmentGroup.id IN (SELECT ag.id FROM assignment_groups ag INNER JOIN assignment_group_students ags ON ag.id = ags.assignment_group_id WHERE ags.student_id = :userId)))',
          { userId, visibleStatus: DocumentStatus.VISIBLE },
        );
      } else if (user.isInstructor && !user.isAdmin) {
        // Instructors can see:
        // 1. Their own documents
        // 2. All documents in courses they teach
        queryBuilder.andWhere(
          '(document.uploaderId = :userId OR assignment.instructorId = :userId)',
          { userId },
        );
      }
      // Admins can see all documents (no additional filtering)

      // Apply custom document-specific filters
      if (searchParams.status) {
        queryBuilder.andWhere('document.status = :status', {
          status: searchParams.status,
        });
      }

      if (searchParams.repositoryType) {
        queryBuilder.andWhere('document.repositoryType = :repositoryType', {
          repositoryType: searchParams.repositoryType,
        });
      }

      if (searchParams.assignmentGroupId) {
        queryBuilder.andWhere(
          'document.assignmentGroupId = :assignmentGroupId',
          {
            assignmentGroupId: searchParams.assignmentGroupId,
          },
        );
      }

      if (searchParams.courseId) {
        queryBuilder.andWhere('assignment.courseId = :courseId', {
          courseId: searchParams.courseId,
        });
      }

      if (searchParams.uploaderId) {
        queryBuilder.andWhere('document.uploaderId = :uploaderId', {
          uploaderId: searchParams.uploaderId,
        });
      }

      if (searchParams.tags && searchParams.tags.length > 0) {
        queryBuilder.andWhere('document.tags && :tags', {
          tags: searchParams.tags,
        });
      }

      if (searchParams.fileExtension) {
        queryBuilder.andWhere('document.fileName ILIKE :fileExtension', {
          fileExtension: `%.${searchParams.fileExtension}`,
        });
      }

      // Prepare filters for applyQueryOptions
      const filters = {};

      // Build sort field for applyQueryOptions
      const sortField = searchParams.sortBy || 'createdAt';
      const sortOrder = searchParams.sortOrder || 'DESC';
      const sortString = sortOrder === 'DESC' ? `-${sortField}` : sortField;

      // Apply query options for search, sorting, and pagination
      const limit = searchParams.limit || 20;
      const offset = searchParams.offset || 0;
      const page = Math.floor(offset / limit) + 1;

      const finalQueryBuilder = applyQueryOptions(
        queryBuilder,
        filters,
        ['document.fileName', 'document.description'], // Search fields
        searchParams.search,
        sortString,
        page,
        limit,
      );

      // Execute query with getManyAndCount for efficiency
      const [documents, total] = await finalQueryBuilder.getManyAndCount();

      // Map to response DTOs
      const documentDtos = documents.map((doc) => this.mapToResponseDto(doc));

      return this.success({
        data: documentDtos ?? [],
        total,
        page: page,
        limit: limit,
        totalPages: Math.ceil(total / limit),
      });
    } catch (error) {
      this.logger.error(
        `Error searching documents: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw known exceptions
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      // Throw generic error for unknown exceptions
      throw new BadRequestException('Failed to search documents');
    }
  }

  private mapToResponseDto(document: Document): DocumentResponseDto {
    return {
      id: document.id,
      fileUrl: document.fileUrl,
      fileName: document.fileName,
      status: document.status,
      description: document.description,
      tags: document.tags,
      threadId: document.threadId,
      repositoryType: document.repositoryType,
      uploaderId: document.uploaderId,
      assignmentGroupId: document.assignmentGroupId,
      courseId: document.courseId,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
    };
  }

  async createDocument(
    createDocumentDto: CreateDocumentDto,
    uploaderId: string,
  ): Promise<CustomResponse> {
    try {
      this.logger.log(
        `Creating document: ${createDocumentDto.fileName} for user: ${uploaderId}`,
        this.loggerMeta,
      );

      // Validate uploader exists
      const uploader = await this.userRepository.findOne({
        where: { id: uploaderId },
      });
      if (!uploader) {
        this.logger.warn(`Uploader not found: ${uploaderId}`, this.loggerMeta);
        throw new NotFoundException('Uploader not found');
      }

      // Validate assignment group exists
      const assignmentGroup = await this.assignmentGroupRepository.findOne({
        where: { id: createDocumentDto.assignmentGroupId },
        relations: ['assignment', 'assignment.course'],
      });
      if (!assignmentGroup) {
        this.logger.warn(
          `Assignment group not found: ${createDocumentDto.assignmentGroupId}`,
          this.loggerMeta,
        );
        throw new NotFoundException('Assignment group not found');
      }

      // Validate course ID matches assignment group's course
      if (assignmentGroup.assignment.courseId !== createDocumentDto.courseId) {
        this.logger.warn(
          `Course ID mismatch: provided ${createDocumentDto.courseId}, expected ${assignmentGroup.assignment.courseId}`,
          this.loggerMeta,
        );
        throw new BadRequestException(
          'Course ID does not match assignment group course',
        );
      }

      // Validate file URL format (basic validation)
      if (!isValidFileUrl(createDocumentDto.fileUrl)) {
        this.logger.warn(
          `Invalid file URL format: ${createDocumentDto.fileUrl}`,
          this.loggerMeta,
        );
        throw new BadRequestException('Invalid file URL format');
      }

      // Create document entity
      const document = this.documentRepository.create({
        uploaderId,
        status: DocumentStatus.HIDDEN,
        ...createDocumentDto,
      });

      // Save document
      const savedDocument = await this.documentRepository.save(document);

      this.logger.log(
        `Document created successfully: ${savedDocument.id}`,
        this.loggerMeta,
      );

      // Return success response
      return this.success(this.mapToResponseDto(savedDocument));
    } catch (error) {
      this.logger.error(
        `Error creating document: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw known exceptions
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      // Throw generic error for unknown exceptions
      throw new BadRequestException('Failed to create document');
    }
  }

  async updateDocument(
    documentId: string,
    updateDocumentDto: UpdateDocumentDto,
    userId: string,
  ): Promise<CustomResponse> {
    try {
      this.logger.log(
        `Updating document: ${documentId} by user: ${userId}`,
        this.loggerMeta,
      );

      // Find the document with relations (excluding soft-deleted)
      const document = await this.documentRepository.findOne({
        where: { id: documentId },
        relations: ['uploader', 'assignmentGroup'],
      });

      if (!document) {
        this.logger.warn(`Document not found: ${documentId}`, this.loggerMeta);
        throw new NotFoundException('Document not found');
      }

      // Validate user exists
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        this.logger.warn(`User not found: ${userId}`, this.loggerMeta);
        throw new NotFoundException('User not found');
      }

      // Authorization check: Only the uploader or admin/instructor can update
      if (
        document.uploaderId !== userId &&
        !user.isAdmin &&
        !user.isInstructor
      ) {
        this.logger.warn(
          `User ${userId} not authorized to update document ${documentId}`,
          this.loggerMeta,
        );
        throw new ForbiddenException('Not authorized to update this document');
      }

      // Business rule: Only instructors can change status from HIDDEN to VISIBLE
      if (
        updateDocumentDto.status === DocumentStatus.VISIBLE &&
        document.status === DocumentStatus.HIDDEN &&
        !user.isInstructor &&
        !user.isAdmin
      ) {
        this.logger.warn(
          `User ${userId} not authorized to make document visible`,
          this.loggerMeta,
        );
        throw new ForbiddenException(
          'Only instructors can make documents visible',
        );
      }

      // Update only provided fields
      const updateData: Partial<Document> = {};
      if (updateDocumentDto.status !== undefined) {
        updateData.status = updateDocumentDto.status;
      }
      if (updateDocumentDto.description !== undefined) {
        updateData.description = updateDocumentDto.description;
      }
      if (updateDocumentDto.tags !== undefined) {
        updateData.tags = updateDocumentDto.tags;
      }

      // Perform the update
      await this.documentRepository.update(documentId, updateData);

      // Fetch the updated document (excluding soft-deleted)
      const updatedDocument = await this.documentRepository.findOne({
        where: { id: documentId },
      });

      if (!updatedDocument) {
        throw new BadRequestException('Failed to retrieve updated document');
      }

      this.logger.log(
        `Document updated successfully: ${documentId}`,
        this.loggerMeta,
      );

      return this.success(this.mapToResponseDto(updatedDocument));
    } catch (error) {
      this.logger.error(
        `Error updating document: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw known exceptions
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      // Throw generic error for unknown exceptions
      throw new BadRequestException('Failed to update document');
    }
  }

  async deleteDocuments(
    documentIds: string[],
    userId: string,
  ): Promise<CustomResponse> {
    try {
      this.logger.log(
        `Bulk deleting ${documentIds.length} documents by user: ${userId}`,
        this.loggerMeta,
      );

      // Validate user exists
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });
      if (!user) {
        this.logger.warn(`User not found: ${userId}`, this.loggerMeta);
        throw new NotFoundException('User not found');
      }

      // Fetch all documents with their relations
      const documents = await this.documentRepository.find({
        where: { id: In(documentIds) },
        relations: ['uploader', 'assignmentGroup'],
      });

      const results: DeleteDocumentResult[] = [];
      let deletedCount = 0;
      let failedCount = 0;

      // Process each document ID
      for (const documentId of documentIds) {
        try {
          const document = documents.find((doc) => doc.id === documentId);

          if (!document) {
            results.push({
              documentId,
              success: false,
              error: 'Document not found',
            });
            failedCount++;
            continue;
          }

          // Authorization check: Only the uploader or admin/instructor can delete
          if (
            document.uploaderId !== userId &&
            !user.isAdmin &&
            !user.isInstructor
          ) {
            results.push({
              documentId,
              success: false,
              error: 'Not authorized to delete this document',
            });
            failedCount++;
            continue;
          }

          // Business rule: Cannot delete TEMPORARY files manually (FR6)
          if (document.repositoryType === DocumentRepositoryType.TEMPORARY) {
            results.push({
              documentId,
              success: false,
              error: 'Cannot manually delete temporary files',
            });
            failedCount++;
            continue;
          }

          // Perform soft delete from database
          await this.documentRepository.softRemove(document);

          results.push({
            documentId,
            success: true,
          });
          deletedCount++;

          this.logger.log(
            `Document soft deleted successfully: ${documentId}`,
            this.loggerMeta,
          );
        } catch (error) {
          this.logger.error(
            `Error deleting document ${documentId}: ${error instanceof Error ? error.message : String(error)}`,
            this.loggerMeta,
            error instanceof Error ? error.stack : undefined,
          );

          results.push({
            documentId,
            success: false,
            error: 'Failed to delete document',
          });
          failedCount++;
        }
      }

      const responseData: DeleteDocumentsResponseDto = {
        totalRequested: documentIds.length,
        totalDeleted: deletedCount,
        totalFailed: failedCount,
        results,
      };

      this.logger.log(
        `Bulk soft deletion completed: ${deletedCount} deleted, ${failedCount} failed`,
        this.loggerMeta,
      );

      return this.success(responseData);
    } catch (error) {
      this.logger.error(
        `Error in bulk delete operation: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw known exceptions
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }

      // Throw generic error for unknown exceptions
      throw new BadRequestException('Failed to delete documents');
    }
  }
}
