import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  UseGuards,
  Req,
} from '@nestjs/common';
import { DocumentsService } from './documents.service';
import {
  CreateDocumentDto,
  UpdateDocumentDto,
  DeleteDocumentsDto,
  DeleteDocumentsResponseDto,
  SearchDocumentsDto,
  DocumentResponseDto,
} from './dto/';
import { CustomResponse } from '@common/http';
import { CustomLogger } from '@common/logging';
import { JwtAuthGuard } from '@common/guards/jwt-auth.guard';
import { Request } from 'express';

interface AuthenticatedRequest extends Request {
  user: {
    userId: string;
    email: string;
    isAdmin: boolean;
    isInstructor: boolean;
  };
}

@Controller('v1/documents')
@UseGuards(JwtAuthGuard)
export class DocumentsController {
  private readonly loggerMeta: any;

  constructor(
    private readonly documentsService: DocumentsService,
    private readonly logger: CustomLogger,
  ) {
    this.loggerMeta = { context: DocumentsController.name };
  }

  @Get()
  async searchDocuments(
    @Query() searchParams: SearchDocumentsDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<CustomResponse> {
    try {
      // Get userId from request or fallback for testing
      const userId = req?.user?.userId ?? searchParams?.userId;

      this.logger.log(
        `Searching documents with params: ${JSON.stringify(searchParams)} by user: ${userId}`,
        this.loggerMeta,
      );

      const result = await this.documentsService.searchDocuments(
        searchParams,
        userId,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error in searchDocuments: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw HttpExceptions as-is (they have proper status codes)
      if (error instanceof HttpException) {
        throw error;
      }

      // Throw generic internal server error for unknown exceptions
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post()
  async createDocument(
    @Body() createDocumentDto: CreateDocumentDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<CustomResponse> {
    try {
      this.logger.log(
        `Creating document: ${createDocumentDto.fileName} for user: ${req?.user?.userId ?? createDocumentDto?.userId}`,
        this.loggerMeta,
      );

      const document = await this.documentsService.createDocument(
        createDocumentDto,
        req?.user?.userId ?? createDocumentDto?.userId,
      );

      this.logger.log(
        `Document created successfully: ${(document.data as DocumentResponseDto).id}`,
        this.loggerMeta,
      );

      return document;
    } catch (error) {
      this.logger.error(
        `Error in createDocument: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw HttpExceptions as-is (they have proper status codes)
      if (error instanceof HttpException) {
        throw error;
      }

      // Throw generic internal server error for unknown exceptions
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  async updateDocument(
    @Param('id') documentId: string,
    @Body() updateDocumentDto: UpdateDocumentDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<CustomResponse> {
    try {
      // Get userId from request or fallback for testing
      const userId = req?.user?.userId ?? updateDocumentDto?.userId;

      this.logger.log(
        `Updating document: ${documentId} by user: ${userId}`,
        this.loggerMeta,
      );

      const document = await this.documentsService.updateDocument(
        documentId,
        updateDocumentDto,
        userId,
      );

      this.logger.log(
        `Document updated successfully: ${(document.data as DocumentResponseDto).id}`,
        this.loggerMeta,
      );

      return document;
    } catch (error) {
      this.logger.error(
        `Error in updateDocument: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw HttpExceptions as-is (they have proper status codes)
      if (error instanceof HttpException) {
        throw error;
      }

      // Throw generic internal server error for unknown exceptions
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete()
  async deleteDocuments(
    @Body() deleteDocumentsDto: DeleteDocumentsDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<CustomResponse> {
    try {
      // Get userId from request or fallback for testing
      const userId = req?.user?.userId ?? deleteDocumentsDto?.userId;

      this.logger.log(
        `Bulk deleting ${deleteDocumentsDto.documentIds.length} documents by user: ${userId}`,
        this.loggerMeta,
      );

      const result = await this.documentsService.deleteDocuments(
        deleteDocumentsDto.documentIds,
        userId,
      );

      this.logger.log(
        `Bulk deletion completed: ${(result.data as DeleteDocumentsResponseDto).totalDeleted} deleted, ${(result.data as DeleteDocumentsResponseDto).totalFailed} failed`,
        this.loggerMeta,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error in deleteDocuments: ${error instanceof Error ? error.message : String(error)}`,
        this.loggerMeta,
        error instanceof Error ? error.stack : undefined,
      );

      // Re-throw HttpExceptions as-is (they have proper status codes)
      if (error instanceof HttpException) {
        throw error;
      }

      // Throw generic internal server error for unknown exceptions
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
