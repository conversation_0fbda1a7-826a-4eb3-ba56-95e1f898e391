import {
  IsOptional,
  IsString,
  IsE<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  DocumentStatus,
  DocumentRepositoryType,
} from '../entities/document.entity';

export enum SortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  FILE_NAME = 'fileName',
  UPLOADER_ID = 'uploaderId',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class SearchDocumentsDto {
  @IsOptional()
  @IsString()
  search?: string; // Search in fileName, description, tags

  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @IsOptional()
  @IsEnum(DocumentRepositoryType)
  repositoryType?: DocumentRepositoryType;

  @IsOptional()
  @IsUUID('4')
  assignmentGroupId?: string;

  @IsOptional()
  @IsUUID('4')
  courseId?: string;

  @IsOptional()
  @IsUUID('4')
  uploaderId?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map((tag) => tag.trim());
    }
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return value;
  })
  tags?: string[];

  @IsOptional()
  @IsString()
  fileExtension?: string; // Filter by file extension (e.g., 'pdf', 'docx')

  @IsOptional()
  @IsEnum(SortField)
  sortBy?: SortField = SortField.CREATED_AT;

  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  offset?: number = 0;

  // TODO: for testing, need remove
  @IsOptional()
  @IsUUID('4')
  userId?: string;
}
