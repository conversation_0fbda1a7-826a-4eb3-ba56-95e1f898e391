import {
  MiddlewareConsumer,
  Module,
  RequestMethod,
  ExecutionContext,
} from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './common/guards/jwt-auth.guard';
import { JwtService } from '@nestjs/jwt';
import databaseConfig from './common/configs/database.config';
import mongodbConfig from './common/database/mongodb/config';
import authConfig from './common/configs/auth.config';
import appConfig from './common/configs/app.config';
import mailConfig from './common/configs/mail.config';
import fileConfig from './common/configs/file.config';
import notificationConfig from './common/configs/notification.config';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmConfigService } from './common/database/typeorm-config.service';
import { CustomLogger } from './common/logging';
import { DataSource, DataSourceOptions } from 'typeorm';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { ThreadsModule } from './modules/threads/threads.module';
import { ThreadGateway } from '@src/websocket/thread.gateway';
import { WebsocketModule } from '@src/websocket/websocket.module';

import {
  AcceptLanguageResolver,
  I18nModule,
  QueryResolver,
  HeaderResolver,
} from 'nestjs-i18n';
import * as path from 'path';
import { requestContextMiddlewareFactory } from './common/middlewares/request-context.middleware';

import { addTransactionalDataSource } from 'typeorm-transactional';

import { HealthModule } from './health/health.module';
import { S3Module } from '@modules/s3/s3.module';
import { DocumentsModule } from '@modules/documents/documents.module';

@Module({
  imports: [
    I18nModule.forRoot({
      fallbackLanguage: 'vi',
      loaderOptions: {
        path: path.join(__dirname, '/common/i18n/'),
        watch: true,
      },
      resolvers: [
        new QueryResolver(['lang', 'l']),
        new HeaderResolver(['x-lang']),
        AcceptLanguageResolver,
      ],
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        databaseConfig,
        mongodbConfig,
        authConfig,
        appConfig,
        mailConfig,
        fileConfig,
        notificationConfig,
      ],
      envFilePath: ['.env'],
    }),
    TypeOrmModule.forRootAsync({
      useClass: TypeOrmConfigService,
      dataSourceFactory: async (options: DataSourceOptions) => {
        return Promise.resolve(
          addTransactionalDataSource(new DataSource(options)),
        );
      },
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get<string>('mongodb.uri'),
        dbName: configService.get<string>('mongodb.dbName'),
        retryWrites: true,
        writeConcern: { w: 'majority' },
        retryAttempts: 5,
        retryDelay: 1000,
      }),
      inject: [ConfigService],
    }),
    HealthModule,
    UsersModule,
    AuthModule,
    WebsocketModule,
    S3Module,
    DocumentsModule,
    // ThreadsModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: class extends JwtAuthGuard {
        canActivate(context: ExecutionContext) {
          // const req = context.switchToHttp().getRequest();
          // const path = req.path;

          return super.canActivate(context);
        }
      },
    },
    CustomLogger,
    JwtService,
    // ThreadGateway,
  ],
  exports: [CustomLogger],
})
export class AppModule {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(
        requestContextMiddlewareFactory(this.jwtService, this.configService),
      )
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
